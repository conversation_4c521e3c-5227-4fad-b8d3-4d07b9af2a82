{"name": "YXG-pos", "version": "1.0.0", "type": "module", "updateServer": "http://127.0.0.1:8083", "scripts": {"dev": "node_modules\\nw\\nwjs-sdk-v0.72.0-win-ia32\\nw.exe ./src", "preview": "node_modules\\nw\\nwjs-sdk-v0.72.0-win-ia32\\nw.exe ./out/package.nw", "dev2": "cmd /k node_modules\\nw\\nwjs-sdk-v0.72.0-win-ia32\\nw.exe ./src --user-data-dir=./user-data-backup --port=9001", "build:win": "node nwbuild.config.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"baidu-aip-sdk": "^2.3.9", "body-parser": "^1.19.0", "cookie-parser": "^1.4.4", "ejs": "^2.6.1", "express": "^4.16.4", "hotkeys-js": "^3.6.6", "hupdater": "^1.5.2", "morgan": "^1.9.1", "nw": "0.72.0-sdk", "prettier": "^3.6.2", "querystring": "^0.2.0", "request": "^2.88.0", "vue-infinite-scroll": "^2.0.2"}, "single-instance": true, "devDependencies": {"@prettier/plugin-oxc": "^0.0.4", "nw-builder": "^4.13.14", "oxlint": "^1.6.0", "prettier-plugin-css-order": "^2.1.2", "prettier-plugin-tailwindcss": "^0.6.13", "unocss": "^66.3.3"}, "packageManager": "npm"}